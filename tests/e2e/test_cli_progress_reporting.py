from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from llama_tune.cli import app
import pytest
from unittest.mock import patch, MagicMock
from llama_tune.core.data_models import OptimalConfiguration, BenchmarkResult, SystemProfile, ModelProfile
from io import StringIO
from rich.console import Console
import re
import subprocess

runner = CliRunner()

def strip_ansi_and_carriage_return_codes(s):
    # Split the string into lines
    lines = s.split('\n')
    cleaned_lines = []
    for line in lines:
        # For each line, split by carriage return and take the last part
        # This handles in-place updates by rich
        final_line_content = line.split('\r')[-1]
        # Strip ANSI escape codes from the final content
        stripped_line = re.sub(r'\x1b\[[0-9;]*[a-zA-Z]', '', final_line_content)
        if stripped_line.strip(): # Only add non-empty lines
            cleaned_lines.append(stripped_line.strip())
    return '\n'.join(cleaned_lines)

@patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile') # Patch the class directly
@patch('llama_tune.benchmarker.benchmarking_engine.run_feasibility_check')
@patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile') # Patch at source
@patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile') # Patch at source
@patch('rich.progress.Progress') # Patch Progress directly
@patch('typer.echo') # Patch typer.echo
@patch('sys.exit') # Patch sys.exit
@patch('os.path.exists') # Patch os.path.exists
@patch('os.path.isfile') # Patch os.path.isfile
@patch('llama_tune.analyzer.analyzer.subprocess.run') # Patch subprocess.run in analyzer
def test_benchmark_progress_reporting(
    mock_subprocess_run,
    mock_is_file,
    mock_exists,
    mock_sys_exit,
    mock_typer_echo,
    mock_get_system_profile,
    mock_get_model_profile,
    mock_run_feasibility_check,
    mock_benchmarking_engine_class,
    mock_progress_class
):
    """
    Tests that the benchmark command displays progress reporting elements.
    """
    # Prevent sys.exit from terminating the test prematurely
    mock_sys_exit.side_effect = lambda *args: None

    # Mock os.path.exists and os.path.isfile to simulate dummy model file
    mock_exists.return_value = True
    mock_is_file.return_value = True

    # Mock subprocess.run for llama-gguf calls
    mock_subprocess_run.return_value = MagicMock(
        stdout="""
model: arch: llama
model: n_layer: 32
model: ftype: Q4_0
""",
        stderr="",
        returncode=0
    )

    # Mock system and model profiles for BenchmarkingEngine's internal calls
    mock_get_system_profile.return_value = SystemProfile(total_ram_gb=16, cpu_cores=8, gpus=[], numa_detected=False, blas_backend="BLIS")
    mock_get_model_profile.return_value = ModelProfile(file_path="dummy/model.gguf", quantization_type="Q4_0", architecture="llama", layer_count=32)

    # Configure the return value of run_benchmark directly on the mocked method
    def mock_run_benchmark_side_effect(*args, **kwargs):
        # Simulate progress updates
        update_callback = kwargs.get('update_progress_callback')
        if update_callback:
            update_callback("Phase 1/2", "Initializing", 0, 100, run_idx=1, num_runs=1)
            update_callback("Phase 1/2", "Running benchmark", 50, 100, run_idx=1, num_runs=1)
            update_callback("Phase 2/2", "Finalizing", 100, 100, run_idx=1, num_runs=1)
        
        return (
            OptimalConfiguration(
                system_profile=mock_get_system_profile.return_value,
                model_profile=mock_get_model_profile.return_value,
                best_benchmark_result=BenchmarkResult(n_gpu_layers=0, prompt_speed_tps=10.0, generation_speed_tps=20.0, batch_size=1, parallel_level=1),
                generated_command="./llama.cpp/main -m dummy/model.gguf -p 'test'",
                notes=[],
                ctx_size=512,
                sampling_parameters={}
            ),
            [] # all_benchmark_results (empty list for this test)
        )

    # Create a MagicMock instance for BenchmarkingEngine and set its run_benchmark side effect
    mock_benchmarking_engine_instance = MagicMock()
    mock_benchmarking_engine_instance.run_benchmark.side_effect = mock_run_benchmark_side_effect
    mock_benchmarking_engine_class.return_value = mock_benchmarking_engine_instance

    # Create a StringIO object to capture the console output
    output_capture = StringIO()
    
    # Configure the mocked Progress class to return a MagicMock instance
    # that uses our test_console when Progress() is called.
    mock_progress_instance = MagicMock()
    mock_progress_instance.__enter__.return_value = mock_progress_instance
    mock_progress_instance.__exit__.return_value = None
    
    # Mock the update method of the Progress instance to write to our StringIO
    def mock_update(*args, **kwargs):
        description = kwargs.get('description', '')
        output_capture.write(description + "\n")

    mock_progress_instance.update.side_effect = mock_update
    mock_progress_class.return_value = mock_progress_instance

    result = runner.invoke(app, [
        "--benchmark",
        "--model-path", "dummy/model.gguf",
        "--ctx-size", "512",
        "--num-runs", "1"
    ])

    # Get the captured output and strip ANSI codes and process carriage returns
    captured_output = strip_ansi_and_carriage_return_codes(output_capture.getvalue())

    assert result.exit_code == 0
    assert "Overall Benchmark Progress" in captured_output
    assert "Phase 1/2" in captured_output
    assert "Phase 2/2" in captured_output
    assert "Run 1/1" in captured_output
    assert "[%]" in captured_output
    assert "Benchmarking complete!" in captured_output
